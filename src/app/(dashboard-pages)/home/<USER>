import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>rumb<PERSON><PERSON>,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { Separator } from "@/components/ui/separator"
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>rigger,
} from "@/components/ui/tabs"

// Updated imports to point to src/components/dashboard
import { ChartAreaInteractive } from "@/components/dashboard/chart-area-interactive"
// import { SectionCards } from "@/components/dashboard/section-cards"
import { RecentSessions } from "@/components/dashboard/recent-sessions"
import { QuickActionPanel } from "@/components/dashboard/quick-action-panel"
// import { NotificationCenter } from "@/components/dashboard/notification-center"
import { PlatformConnectionStatus } from "@/components/dashboard/platform-connection-status";

// Import the actions
import {
  getPlatformConnectionStatuses,
  getRecentTasks,
  getEngagementChartData,
  getModeActivationProgress, // Import the new action
} from "./actions";
import { createClient } from "@/supabase/client/server";

// Make the component async
export default async function HomePage() {
  // Add authentication check first
  const supabase = await createClient();
  const { data: { user }, error: authError } = await supabase.auth.getUser();

  if (authError || !user) {
    console.error('HomePage auth check failed:', authError);
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <h2 className="text-lg font-semibold mb-2">Authentication Required</h2>
          <p className="text-muted-foreground">Please sign in to access the dashboard.</p>
        </div>
      </div>
    );
  }

  // Fetch data using server actions
  const { data: platformStatuses, error: platformError } = await getPlatformConnectionStatuses();
  const { data: recentTasksData, error: tasksError } = await getRecentTasks(); // Default limit is 5
  const { data: engagementData, error: engagementError } = await getEngagementChartData(); // Default days is 30
  const { activeModes, totalModes, error: progressError } = await getModeActivationProgress(); // Correctly destructure the result from getModeActivationProgress

  // TODO: Add error handling display if needed
  if (platformError) console.error("Platform Status Error:", platformError);
  if (tasksError) console.error("Recent Tasks Error:", tasksError);
  if (engagementError) console.error("Engagement Data Error:", engagementError);
  if (progressError) console.error("Mode Progress Error:", progressError);

  return (
    <div className="@container/main flex flex-1 flex-col gap-4 p-4 md:gap-6 md:p-6">
      {/* Breadcrumbs and Separator first (or wherever they were originally) */}
      <Breadcrumb className="hidden font-medium md:flex">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="#">Dashboard</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Overview</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
      <Separator className="my-2 hidden md:block" />

      {/* Restore the two-column grid */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 md:gap-6">
        {/* Column 1: Tabs with Chart */}
        <div className="flex flex-col gap-4 md:gap-6">
          <Tabs defaultValue="x" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="x">X.com</TabsTrigger>
              <TabsTrigger value="linkedin">LinkedIn</TabsTrigger>
              <TabsTrigger value="reddit">Reddit</TabsTrigger>
            </TabsList>
            <TabsContent value="x">
              {/* Pass fetched data to the chart */}
              <ChartAreaInteractive chartData={engagementData || []} />
            </TabsContent>
            <TabsContent value="linkedin">
              {/* Placeholder for LinkedIn chart */}
              <div className="flex h-[418px] items-center justify-center rounded-lg border border-dashed p-4"> LinkedIn Chart Data </div>
            </TabsContent>
            <TabsContent value="reddit">
              {/* Placeholder for Reddit chart */}
              <div className="flex h-[418px] items-center justify-center rounded-lg border border-dashed p-4"> Reddit Chart Data </div>
            </TabsContent>
          </Tabs>
        </div>

        {/* Column 2: Platform Status and Quick Actions */}
        <div className="flex flex-col gap-4 md:gap-6">
          {/* Pass fetched data to PlatformConnectionStatus */}
          <PlatformConnectionStatus connections={platformStatuses || []} />
          {/* Pass progress data to the updated QuickActionPanel */}
          <QuickActionPanel
            activeModes={activeModes ?? 0}
            totalModes={totalModes ?? 7} // Default to 7 if totalModes is not returned
            error={progressError}
          />
          {/* NotificationCenter could go here if uncommented */}
          {/* <NotificationCenter /> */}
        </div>
      </div>

      {/* Row below the grid: Recent Sessions */}
      <div>
        {/* Pass fetched data to RecentSessions */}
        <RecentSessions sessions={recentTasksData || []} />
        {/* SectionCards could go here if uncommented */}
        {/* <SectionCards /> */}
      </div>
    </div>
  );
}
