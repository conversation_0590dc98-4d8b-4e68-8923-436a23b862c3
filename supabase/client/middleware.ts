import { createServerClient, type CookieOptions } from "@supabase/ssr";
import { type NextRequest, NextResponse } from "next/server";

// Define protected paths (adjust as needed)
const protectedPaths = ['/home', '/profile', '/engage', '/settings', '/history', '/analytics', '/help'];
// Define public paths that logged-in users should be redirected away from
const publicOnlyPaths = ['/sign-in']; // Add others like /forgot-password if needed

export const updateSession = async (request: NextRequest) => {
  // This `try/catch` block is only here for the interactive tutorial.
  // Feel free to remove once you have Supabase connected.
  try {
    // Create an unmodified response
    let response = NextResponse.next({
      request: {
        headers: request.headers,
      },
    });

    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return request.cookies.get(name)?.value;
          },
          set(name: string, value: string, options: CookieOptions) {
            // Only set the cookie on the outgoing response
            response.cookies.set({
              name,
              value,
              ...options,
            });
          },
          remove(name: string, options: CookieOptions) {
            // Only set the cookie on the outgoing response
            response.cookies.set({
              name,
              value: '', // Set value to empty string
              ...options,
            });
          },
        },
      },
    );

    // Refresh session if expired - required for Server Components
    // Important: Await the result of getSession()
    const {
      data: { session },
    } = await supabase.auth.getSession();

    // No need to manually call setSession here, getSession handles the refresh and cookie updates via the handlers above.

    // It's generally recommended to call `getSession` instead of `getUser` in middleware
    // as `getUser` might make an extra network request if the session is not fresh.
    // `getSession` relies on the cookie and refreshes if necessary implicitly.
    const user = session?.user; // Get user from the refreshed session

    const pathname = request.nextUrl.pathname;

    // Redirect unauthenticated users trying to access protected paths
    if (!user && protectedPaths.some(path => pathname.startsWith(path))) {
        console.log(`Redirecting unauthenticated user from ${pathname} to /sign-in`);
        return NextResponse.redirect(new URL('/sign-in', request.url));
    }

    // Redirect authenticated users trying to access public-only paths (like sign-in page)
    if (user && publicOnlyPaths.some(path => pathname.startsWith(path))) {
        console.log(`Redirecting authenticated user from ${pathname} to /home`);
        return NextResponse.redirect(new URL('/home', request.url)); // Redirect to dashboard home
    }

    // Optional: Redirect logged-in users from root to dashboard home
    if (user && pathname === '/') {
       return NextResponse.redirect(new URL('/home', request.url));
    }

    // Return the potentially modified response (with updated cookies)
    return response;
  } catch (e) {
    // If you are here, a Supabase client could not be created!
    // This is likely because you have not set up environment variables.
    // Check out http://localhost:3000 for Next Steps.
    console.error("Error creating Supabase client in middleware:", e); // Log the error
    return NextResponse.next({
      request: {
        headers: request.headers,
      },
    });
  }
};

// Apply the middleware to relevant paths
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * Feel free to modify this pattern to include more paths.
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
};
